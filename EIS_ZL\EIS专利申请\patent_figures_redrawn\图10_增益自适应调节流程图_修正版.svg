<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: Arial, sans-serif; font-size: 11px; text-anchor: middle; }
      .decision-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .param-text { font-family: Arial, sans-serif; font-size: 9px; text-anchor: middle; fill: #666; }
      .branch-text { font-family: Arial, sans-serif; font-size: 9px; text-anchor: middle; fill: #333; }
      .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .loop-connection { stroke: #666; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
      .gain-low { fill: #c8e6c9; stroke: #2e7d32; stroke-width: 2; }
      .gain-mid { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
      .gain-high { fill: #ffcdd2; stroke: #d32f2f; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="450" y="30" class="title">图10 增益自适应调节流程图</text>
  
  <!-- 开始 -->
  <ellipse cx="450" cy="80" rx="60" ry="25" fill="#e3f2fd" stroke="#333" stroke-width="2"/>
  <text x="450" y="87" class="step-text">开始</text>
  
  <!-- 初始化增益 -->
  <rect x="370" y="130" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="450" y="155" class="step-text">初始化增益设置</text>
  
  <!-- 信号采集 -->
  <rect x="370" y="200" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="450" y="225" class="step-text">信号采集</text>
  
  <!-- 信号幅度检测 -->
  <rect x="370" y="270" width="160" height="40" fill="#e1f5fe" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="450" y="295" class="step-text">信号幅度检测</text>
  
  <!-- 幅度范围判断 -->
  <polygon points="450,340 520,370 450,400 380,370" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="450" y="365" class="decision-text">信号幅度</text>
  <text x="450" y="380" class="decision-text">范围判断</text>
  
  <!-- 信号过小分支 -->
  <rect x="150" y="450" width="120" height="50" class="gain-high" rx="5"/>
  <text x="210" y="470" class="step-text">信号过小</text>
  <text x="210" y="485" class="param-text">增加增益</text>
  
  <!-- 信号适中分支 -->
  <rect x="390" y="450" width="120" height="50" class="gain-mid" rx="5"/>
  <text x="450" y="470" class="step-text">信号适中</text>
  <text x="450" y="485" class="param-text">保持增益</text>
  
  <!-- 信号过大分支 -->
  <rect x="630" y="450" width="120" height="50" class="gain-low" rx="5"/>
  <text x="690" y="470" class="step-text">信号过大</text>
  <text x="690" y="485" class="param-text">减少增益</text>
  
  <!-- 增益调节算法 -->
  <rect x="100" y="540" width="140" height="60" fill="#ffebee" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="170" y="565" class="step-text">增益增加算法</text>
  <text x="170" y="580" class="param-text">Gain = Gain × 1.5</text>
  <text x="170" y="595" class="param-text">最大增益: 64倍</text>
  
  <rect x="380" y="540" width="140" height="60" fill="#fff9c4" stroke="#f57f17" stroke-width="1" rx="3"/>
  <text x="450" y="565" class="step-text">增益保持</text>
  <text x="450" y="580" class="param-text">Gain = Gain</text>
  <text x="450" y="595" class="param-text">无调节</text>
  
  <rect x="660" y="540" width="140" height="60" fill="#e8f5e8" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="730" y="565" class="step-text">增益减少算法</text>
  <text x="730" y="580" class="param-text">Gain = Gain × 0.7</text>
  <text x="730" y="595" class="param-text">最小增益: 1倍</text>
  
  <!-- 增益限制检查 -->
  <polygon points="450,650 520,680 450,710 380,680" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="450" y="675" class="decision-text">增益范围</text>
  <text x="450" y="690" class="decision-text">检查</text>
  
  <!-- 增益限制处理 -->
  <rect x="600" y="660" width="120" height="40" fill="#ffcdd2" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="660" y="685" class="step-text">增益限制处理</text>
  
  <!-- 信号质量评估 -->
  <rect x="370" y="760" width="160" height="40" fill="#e1f5fe" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="450" y="785" class="step-text">信号质量评估</text>
  
  <!-- 质量判断 -->
  <polygon points="450,830 520,860 450,890 380,860" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="450" y="855" class="decision-text">信号质量</text>
  <text x="450" y="870" class="decision-text">达标?</text>
  
  <!-- 调节完成 -->
  <ellipse cx="650" cy="860" rx="80" ry="25" fill="#c8e6c9" stroke="#333" stroke-width="2"/>
  <text x="650" y="867" class="step-text">调节完成</text>
  
  <!-- 连接线 -->
  <!-- 主流程 -->
  <line x1="450" y1="105" x2="450" y2="130" class="connection"/>
  <line x1="450" y1="170" x2="450" y2="200" class="connection"/>
  <line x1="450" y1="240" x2="450" y2="270" class="connection"/>
  <line x1="450" y1="310" x2="450" y2="340" class="connection"/>
  
  <!-- 幅度判断分支 -->
  <line x1="400" y1="370" x2="210" y2="450" class="connection"/>
  <text x="305" y="410" class="branch-text">过小</text>
  
  <line x1="450" y1="400" x2="450" y2="450" class="connection"/>
  <text x="465" y="425" class="branch-text">适中</text>
  
  <line x1="500" y1="370" x2="690" y2="450" class="connection"/>
  <text x="595" y="410" class="branch-text">过大</text>
  
  <!-- 增益调节算法连接 -->
  <line x1="210" y1="500" x2="170" y2="540" class="connection"/>
  <line x1="450" y1="500" x2="450" y2="540" class="connection"/>
  <line x1="690" y1="500" x2="730" y2="540" class="connection"/>
  
  <!-- 算法到增益检查 -->
  <line x1="170" y1="600" x2="400" y2="650" class="connection"/>
  <line x1="450" y1="600" x2="450" y2="650" class="connection"/>
  <line x1="730" y1="600" x2="500" y2="650" class="connection"/>
  
  <!-- 增益限制分支 -->
  <line x1="520" y1="680" x2="600" y2="680" class="connection"/>
  <text x="560" y="675" class="branch-text">超限</text>
  
  <line x1="660" y1="700" x2="450" y2="760" class="connection"/>
  
  <!-- 增益检查通过 -->
  <line x1="450" y1="710" x2="450" y2="760" class="connection"/>
  <text x="465" y="735" class="branch-text">正常</text>
  
  <!-- 质量评估到判断 -->
  <line x1="450" y1="800" x2="450" y2="830" class="connection"/>
  
  <!-- 质量判断分支 -->
  <line x1="520" y1="860" x2="570" y2="860" class="connection"/>
  <text x="545" y="855" class="branch-text">达标</text>
  
  <!-- 质量不达标循环 -->
  <line x1="380" y1="860" x2="100" y2="860" class="loop-connection"/>
  <text x="240" y="855" class="branch-text">不达标</text>
  <line x1="100" y1="860" x2="100" y2="225" class="loop-connection"/>
  <line x1="100" y1="225" x2="370" y2="225" class="loop-connection"/>
  
  <!-- 算法参数说明 -->
  <rect x="50" y="920" width="800" height="70" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="3"/>
  <text x="450" y="940" class="step-text">增益自适应调节参数</text>
  <text x="60" y="960" class="param-text">• 目标信号幅度: 50-80% ADC满量程</text>
  <text x="60" y="975" class="param-text">• 增益调节步长: ±30% (粗调), ±10% (细调)</text>
  
  <text x="450" y="960" class="param-text">• 增益范围: 1-64倍 (6位可调)</text>
  <text x="450" y="975" class="param-text">• 调节时间: <100ms</text>
  
  <text x="650" y="960" class="param-text">• 稳定判据: 连续3次测量偏差<5%</text>
  <text x="650" y="975" class="param-text">• 最大调节次数: 10次</text>
</svg>
