<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .module { font-family: "Microsoft YaHei", Aria<PERSON>; font-size: 11px; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .connection { stroke: #666; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
      .special-connection { stroke: #999; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="450" y="35" class="title">图1 系统整体架构图</text>
  
  <!-- 第一层：用户界面模块 -->
  <rect x="350" y="60" width="200" height="50" fill="#e1f5fe" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="90" class="module">用户界面模块</text>
  
  <!-- 第二层：主控制器 -->
  <rect x="350" y="150" width="200" height="50" fill="#f3e5f5" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="180" class="module">主控制器</text>
  
  <!-- 第三层：三个并排模块 -->
  <rect x="120" y="250" width="160" height="50" fill="#fff3e0" stroke="#333" stroke-width="1" rx="5"/>
  <text x="200" y="280" class="module">DNB1101BB芯片模块</text>
  
  <rect x="370" y="250" width="160" height="50" fill="#fff3e0" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="280" class="module">外部电流源模块</text>
  
  <rect x="620" y="250" width="160" height="50" fill="#fff3e0" stroke="#333" stroke-width="1" rx="5"/>
  <text x="700" y="280" class="module">通信接口模块</text>
  
  <!-- 第四层：对应的电路模块 -->
  <rect x="120" y="340" width="160" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="200" y="370" class="module">阻抗测量电路</text>
  
  <rect x="370" y="340" width="160" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="370" class="module">电流控制电路</text>
  
  <rect x="620" y="340" width="160" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="700" y="370" class="module">Modbus RTU通信</text>
  
  <!-- 第五层：信号调理电路 -->
  <rect x="250" y="430" width="200" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="350" y="460" class="module">信号调理电路</text>
  
  <!-- 第六层：ADC采样 -->
  <rect x="350" y="520" width="200" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="550" class="module">ADC采样</text>
  
  <!-- 第七层：数据处理模块 -->
  <rect x="350" y="610" width="200" height="50" fill="#e8f5e8" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="640" class="module">数据处理模块</text>
  
  <!-- 第八层：三个算法模块 -->
  <rect x="80" y="700" width="160" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="160" y="730" class="module">多维参数提取算法</text>
  
  <rect x="370" y="700" width="160" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="730" class="module">九档智能分组决策</text>
  
  <rect x="660" y="700" width="160" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="740" y="730" class="module">增益自适应调节</text>
  
  <!-- 第九层：测试结果输出 -->
  <rect x="350" y="790" width="200" height="50" fill="#fce4ec" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="820" class="module">测试结果输出</text>
  
  <!-- 第十层：三个输出模块 -->
  <rect x="150" y="880" width="120" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="210" y="910" class="module">数据存储</text>
  
  <rect x="390" y="880" width="120" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="450" y="910" class="module">结果显示</text>
  
  <rect x="630" y="880" width="120" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="690" y="910" class="module">上位机通信</text>
  
  <!-- 主要连接线 -->
  <!-- 用户界面 → 主控制器 -->
  <line x1="450" y1="110" x2="450" y2="150" class="arrow"/>
  
  <!-- 主控制器 → 三个模块 -->
  <line x1="400" y1="200" x2="200" y2="250" class="arrow"/>
  <line x1="450" y1="200" x2="450" y2="250" class="arrow"/>
  <line x1="500" y1="200" x2="700" y2="250" class="arrow"/>
  
  <!-- 三个模块 → 对应电路 -->
  <line x1="200" y1="300" x2="200" y2="340" class="arrow"/>
  <line x1="450" y1="300" x2="450" y2="340" class="arrow"/>
  <line x1="700" y1="300" x2="700" y2="340" class="arrow"/>
  
  <!-- 电路 → 信号调理（优化的连接方式） -->
  <path d="M 200 390 L 200 410 L 300 410 L 300 430" class="arrow"/>
  <path d="M 450 390 L 450 410 L 400 410 L 400 430" class="arrow"/>
  
  <!-- 信号调理 → ADC -->
  <line x1="400" y1="480" x2="450" y2="520" class="arrow"/>
  
  <!-- ADC → 数据处理 -->
  <line x1="450" y1="570" x2="450" y2="610" class="arrow"/>
  
  <!-- 数据处理 → 三个算法 -->
  <line x1="400" y1="660" x2="160" y2="700" class="arrow"/>
  <line x1="450" y1="660" x2="450" y2="700" class="arrow"/>
  <line x1="500" y1="660" x2="740" y2="700" class="arrow"/>
  
  <!-- 三个算法 → 测试结果 -->
  <line x1="160" y1="750" x2="400" y2="790" class="arrow"/>
  <line x1="450" y1="750" x2="450" y2="790" class="arrow"/>
  <line x1="740" y1="750" x2="500" y2="790" class="arrow"/>
  
  <!-- 测试结果 → 三个输出 -->
  <line x1="400" y1="840" x2="210" y2="880" class="arrow"/>
  <line x1="450" y1="840" x2="450" y2="880" class="arrow"/>
  <line x1="500" y1="840" x2="690" y2="880" class="arrow"/>
  
  <!-- 通信模块的特殊连接（优化路径） -->
  <path d="M 780 365 L 820 365 L 820 950 L 750 950 L 750 930" class="special-connection"/>
  
  <!-- 添加连接点标识 -->
  <circle cx="820" cy="365" r="3" fill="#666"/>
  <circle cx="820" cy="950" r="3" fill="#666"/>
  
  <!-- 添加说明文字 -->
  <text x="830" y="660" class="module" style="font-size: 9px; fill: #666;">通信连接</text>
</svg>
